/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         product_id:
 *           type: string
 *         product_name:
 *           type: string
 *         description:
 *           type: string
 *         category:
 *           type: string
 *         price:
 *           type: number
 *         latest_price:
 *           type: number
 *         original_price:
 *           type: number
 *         brand_name:
 *           type: string
 *         thc_percentage:
 *           type: string
 *         cbd_percentage:
 *           type: string
 *         image_url:
 *           type: string
 *         weight:
 *           type: string
 *         display_weight:
 *           type: string
 *         location_id:
 *           type: integer
 *         retailer_id:
 *           type: string
 *     ProductListResponse:
 *       type: object
 *       properties:
 *         products:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Product'
 *         pagination:
 *           type: object
 *           properties:
 *             totalPages:
 *               type: integer
 *             currentPage:
 *               type: integer
 *             cursor:
 *               type: string
 *               nullable: true
 *             total:
 *               type: integer
 *             per_page:
 *               type: integer
 *     ProductUploadRequest:
 *       type: object
 *       properties:
 *         product_data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Product'
 *         reindex:
 *           type: boolean
 *           description: Whether to reindex the products after import
 *         enhance_with_ai:
 *           type: boolean
 *           description: Whether to enhance product data using AI
 *     ProductUploadResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         imported:
 *           type: integer
 *         skipped:
 *           type: integer
 *         errors:
 *           type: array
 *           items:
 *             type: string
 *     ChatMessageRequest:
 *       type: object
 *       required:
 *         - message
 *       properties:
 *         message:
 *           type: string
 *           description: The user's message
 *         chat_id:
 *           type: string
 *           nullable: true
 *           description: Optional chat ID for continuing a conversation
 *     ChatMessageResponse:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [ai]
 *         content:
 *           type: string
 *         message_id:
 *           type: string
 *         chat_id:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             suggested_next_questions:
 *               type: array
 *               items:
 *                 type: string
 *             products:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Product'
 */

/**
 * @swagger
 * tags:
 *   name: PublicProduct
 *   description: |
 *     Public endpoints for product data and chat with dual authentication support.
 *
 *     ## Authentication Overview
 *
 *     All endpoints require a location API key for access. User authentication is optional but provides enhanced features.
 *
 *     ### API Key Authentication (Required)
 *     - **Header**: `Authorization: Bearer your_location_api_key`
 *     - **Purpose**: Identifies the location/dispensary
 *     - **Required for**: All endpoints
 *
 *     ### User Authentication (Optional)
 *     - **Header**: `x-user-token: user_jwt_token`
 *     - **Purpose**: Links actions to user account
 *     - **Benefits**: Chat history persistence, order tracking, personalized recommendations
 *
 *     ## Usage Examples
 *
 *     ### Anonymous Access (API Key Only)
 *     ```
 *     curl -H "Authorization: Bearer sk_live_abc123..." /public/products/chat
 *     ```
 *
 *     ### Authenticated Access (API Key + User Token)
 *     ```
 *     curl -H "Authorization: Bearer sk_live_abc123..." \
 *          -H "x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
 *          /public/products/chat
 *     ```
 *
 *     ## Feature Comparison
 *
 *     | Feature | Anonymous | Authenticated |
 *     |---------|-----------|---------------|
 *     | Product search | ✅ | ✅ |
 *     | Product recommendations | ✅ | ✅ |
 *     | Chat responses | ✅ | ✅ |
 *     | Chat history | ❌ | ✅ |
 *     | Saved chats | ❌ | ✅ |
 *     | Order placement | ✅ | ✅ |
 *     | Order tracking | ❌ | ✅ |
 */

import Router from "@koa/router";
import { getLocationApiKey } from "../locations/LocationService";
import { logger } from "../config/logger";
import { importProducts } from "./ProductImport";
import parse from "../storage/FileStream";
import { pagedProducts } from "./ProductRepository";
import { searchParamsSchema, SearchSchema } from "../core/searchParams";
import { extractQueryParams } from "../utilities";
import { RequestError } from "../core/errors";
import { LocationApiKey } from "../locations/LocationApiKey";
import { Next, ParameterizedContext } from "koa";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import Location from "../locations/Location";
import { JSONSchemaType, validate } from "../core/validate";
import { ChatService } from "../chats/ChatService";
import { SmokeyAIService } from "../chats/SmokeyAIService";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import App from "../app";
import { Message } from "../chats/models/types";
import { Product } from "./Product";
import { MenuSettings, MenuSettingsParams } from "../content/MenuSettings";

// Define a state interface for our router that includes the API key and location ID
interface ApiKeyState {
  apiKey?: LocationApiKey;
  locationId?: number;
  location?: Location;
}

// Chat message interface
interface ChatMessageParams {
  message: string;
  chat_id?: string | null;
}

// Interface for VIP user creation
interface VipUserParams {
  external_id: string;
  email?: string;
  phone?: string;
  first_name?: string;
  last_name?: string;
  timezone?: string;
  locale?: string;
  vip?: boolean;
  data?: any;
}

// Interface for formatted chat messages
interface FormattedChatMessage {
  id: number | string;
  message_id: number | string;
  chat_id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
  data?: any;
}

// Interface for product data returned from vector search
interface ProductVectorResult {
  id?: string | number;
  product_id?: string | number;
  product_name?: string;
  description?: string;
  category?: string;
  price?: number;
  latest_price?: number;
  brand_name?: string;
  thc_percentage?: string | number;
  cbd_percentage?: string | number;
  image_url?: string;
  weight?: string;
  display_weight?: string;
  [key: string]: any;
}

// New interfaces for ecommerce features
interface FilterOptions {
  brands: string[];
  categories: string[];
  subcategories: string[];
}

interface ProductVariant {
  id: number;
  product_id: string;
  display_weight: string;
  latest_price: number;
  medical: boolean;
  recreational: boolean;
  external_id?: string;
  url?: string;
  inventory_quantity?: number;
  out_of_stock?: boolean;
  slug?: string;
}

interface GroupedProduct {
  meta_sku: string;
  product_name: string;
  brand_name?: string;
  category?: string;
  subcategory?: string;
  description?: string;
  image_url?: string;
  product_tags?: string[];
  percentage_thc?: number;
  percentage_cbd?: number;
  rating?: number;
  reviews_count?: number;
  variants: ProductVariant[];
  base_price: number;
  price_range: {
    min: number;
    max: number;
  };
  // AI enhanced fields
  mood?: string[];
  effects?: any;
  product_description?: string;
  short_description?: string;
  // Inventory and metadata
  inventory_quantity?: number;
  out_of_stock?: boolean;
  slug?: string;
  // Featured product fields
  is_featured?: boolean;
  is_top_pick?: boolean;
  featured_sort_order?: number;
  featured_active?: boolean;
}

const chatMessageSchema: JSONSchemaType<ChatMessageParams> = {
  type: "object",
  required: ["message"],
  properties: {
    message: { type: "string", minLength: 1 },
    chat_id: { type: "string", nullable: true },
  },
  additionalProperties: false,
};

const vipUserSchema: JSONSchemaType<VipUserParams> = {
  type: "object",
  required: ["external_id"],
  properties: {
    external_id: { type: "string", minLength: 1 },
    email: { type: "string", nullable: true },
    phone: { type: "string", nullable: true },
    first_name: { type: "string", nullable: true },
    last_name: { type: "string", nullable: true },
    timezone: { type: "string", nullable: true },
    locale: { type: "string", nullable: true },
    vip: { type: "boolean", nullable: true },
    data: { type: "object", nullable: true, additionalProperties: true },
  },
  additionalProperties: false,
};

// Helper function to build SMOKEY system prompt for public controller
async function buildSmokeySystemPrompt(locationId: number): Promise<string> {
  try {
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new Error("Database connection not available");
    }

    // Get competitors for this location
    const competitors = await db("location_competitors")
      .where({ location_id: locationId })
      .select("name", "competitor_place_id");

    // Format competitor info
    const compString =
      competitors.length > 0
        ? `Competitors for this location: ${competitors
            .map((c) => c.name)
            .join(", ")}`
        : "No competitor data available for this location.";

    // Get location info
    const location = await db("locations").where({ id: locationId }).first();

    const locationInfo = location
      ? `Location: id: ${location.id}, name: ${location.name}, city: ${location.city}, state: ${location.state}`
      : "No location information available.";

    return `You are SMOKEY, your friendly AI budtender at ${
      location?.name || "our dispensary"
    }!

${locationInfo}

I'm here to help you find the perfect cannabis products and answer any questions you might have about cannabis. Think of me as your personal guide to our selection!

What I can help you with:
• Product recommendations based on your preferences and needs
• Information about different strains, effects, and consumption methods
• Questions about THC/CBD content and potency
• General cannabis education and safety tips
• Information about our current products and pricing

Guidelines for our conversation:
1. I'm here to serve YOU as a customer - ask me anything about cannabis or our products!
2. I'll give you honest, helpful advice to find what works best for you
3. I'll explain things in friendly, easy-to-understand terms
4. I'll always prioritize your safety and responsible consumption
5. I'll let you know about pricing and product availability when I can

My responses will be:
- Friendly and conversational (like talking to a knowledgeable friend)
- Focused on YOUR needs as a customer
- Educational but not overwhelming
- Honest about what I do and don't know

Product Recommendations:
- When you ask for "best" products, I'll assume you mean popular or highly-rated items, but I'll ask if you want something specific like strongest, best value, etc.
- I'll explain WHY I'm recommending certain products based on your needs
- I'll always suggest talking to our in-store budtenders for the final decision - they can see our current inventory and answer specific questions I might not have answers to

Remember: I'm here to help you learn and find great products, but always consume responsibly and follow local laws!`;
  } catch (error) {
    logger.error("Error building SMOKEY system prompt:", error);
    return `You are SMOKEY, a cannabis dispensary assistant. Answer questions professionally and accurately.`;
  }
}

// Create router for public product endpoints
const router = new Router<ApiKeyState>({
  prefix: "/public/products",
});

// Middleware to authenticate with API key
const apiKeyMiddleware = async (
  ctx: ParameterizedContext<ApiKeyState>,
  next: Next
) => {
  try {
    // Get API key from Authorization header
    const authHeader = String(ctx.request.headers.authorization || "");
    let token: string | undefined;

    if (authHeader.startsWith("Bearer ")) {
      token = authHeader.substring(7, authHeader.length);
    }

    if (!token) {
      logger.warn({
        message: "PublicProductController: Missing API key",
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("API key is required", 401);
    }

    // Log the API key being used (partially masked for security)
    const maskedToken =
      token.length > 8
        ? `${token.substring(0, 4)}...${token.substring(token.length - 4)}`
        : "***";

    logger.info({
      message: "PublicProductController: API key authentication attempt",
      masked_token: maskedToken,
      path: ctx.path,
    });

    // Validate API key
    const apiKey = await getLocationApiKey(token);
    if (!apiKey) {
      logger.warn({
        message: "PublicProductController: Invalid API key",
        masked_token: maskedToken,
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("Invalid API key", 401);
    }

    // Store API key and location ID in context state
    ctx.state.apiKey = apiKey;
    ctx.state.locationId = apiKey.location_id;

    // Get full location details for retailer_id
    const location = await Location.find(apiKey.location_id);
    if (!location) {
      logger.warn({
        message: "PublicProductController: Location not found",
        location_id: apiKey.location_id,
        path: ctx.path,
      });
      throw new RequestError("Location not found", 404);
    }

    logger.info({
      message: "PublicProductController: Authentication successful",
      location_id: apiKey.location_id,
      path: ctx.path,
    });

    ctx.state.location = location;

    return next();
  } catch (error) {
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 401;
      ctx.body = { error: error.message };
    } else {
      logger.error({
        message: "PublicProductController: Unexpected auth error",
        error: error instanceof Error ? error.message : "Unknown error",
        path: ctx.path,
      });
      ctx.status = 500;
      ctx.body = { error: "Authentication error" };
    }
    // Return here to prevent execution from continuing
  }
};

// Apply API key middleware to all routes
router.use(apiKeyMiddleware);

/**
 * @swagger
 * /public/products:
 *   get:
 *     summary: List Public Products (Grouped by MetaSKU)
 *     description: Get a paginated list of products grouped by metaSKU with variants for ecommerce. Use ?grouped=false for individual products.
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: per_page
 *         schema:
 *           type: integer
 *           default: 25
 *         description: Number of items per page
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (for compatibility)
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: ""
 *           enum: ["", price_low_high, price_high_low, thc_low_high, thc_high_low, name_az, name_za]
 *         description: Sort option - empty string for Recently Added (default), or specific sort type
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort direction
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query for product name, brand, category, etc.
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Filter by brand name
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: subcategory
 *         schema:
 *           type: string
 *         description: Filter by subcategory
 *       - in: query
 *         name: grouped
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether to group products by metaSKU (default true for ecommerce)
 *     responses:
 *       200:
 *         description: List of grouped products with variants (default) or individual products
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     oneOf:
 *                       - $ref: '#/components/schemas/Product'
 *                       - type: object
 *                         properties:
 *                           meta_sku:
 *                             type: string
 *                           product_name:
 *                             type: string
 *                           brand_name:
 *                             type: string
 *                           category:
 *                             type: string
 *                           subcategory:
 *                             type: string
 *                           variants:
 *                             type: array
 *                             items:
 *                               type: object
 *                           base_price:
 *                             type: number
 *                           price_range:
 *                             type: object
 *                 pagination:
 *                   type: object
 *       401:
 *         description: API key missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get("/", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Extract filter parameters
    const brandFilter = ctx.query.brand as string;
    const categoryFilter = (ctx.query.category as string)?.split(",") || [];
    const subcategoryFilter = ctx.query.subcategory as string;
    const searchQuery = (ctx.query.q as string)?.trim();
    const grouped = ctx.query.grouped !== "false"; // Default to true, allow override with ?grouped=false

    // Get page size from query params
    const pageSize = parseInt((ctx.query.per_page as string) || "25", 10);
    const page = Math.max(1, parseInt((ctx.query.page as string) || "1", 10));
    const offset = (page - 1) * pageSize;

    // Price and THC filters
    const minPrice = ctx.query.min_price
      ? parseFloat(ctx.query.min_price as string)
      : undefined;
    const maxPrice = ctx.query.max_price
      ? parseFloat(ctx.query.max_price as string)
      : undefined;
    const minThc = ctx.query.min_thc
      ? parseFloat(ctx.query.min_thc as string)
      : undefined;
    const maxThc = ctx.query.max_thc
      ? parseFloat(ctx.query.max_thc as string)
      : undefined;

    // Parse sorting parameters from frontend
    const sortParam = (ctx.query.sort as string) || "";
    let sort = "created_at";
    let direction = "desc";

    // Map frontend sort values to database fields and directions
    switch (sortParam) {
      case "price_low_high":
        sort = "latest_price";
        direction = "asc";
        break;
      case "price_high_low":
        sort = "latest_price";
        direction = "desc";
        break;
      case "thc_low_high":
        sort = "percentage_thc";
        direction = "asc";
        break;
      case "thc_high_low":
        sort = "percentage_thc";
        direction = "desc";
        break;
      case "name_az":
        sort = "product_name";
        direction = "asc";
        break;
      case "name_za":
        sort = "product_name";
        direction = "desc";
        break;
      default:
        // Recently Added (empty string) or any unrecognized value
        sort = "created_at";
        direction = "desc";
        break;
    }

    // Allow manual override of direction if provided
    if (ctx.query.direction) {
      direction =
        (ctx.query.direction as string).toLowerCase() === "asc"
          ? "asc"
          : "desc";
    }

    // Build base query with filters and featured products join - only active products
    let baseQuery = db("products")
      .where("products.location_id", ctx.state.locationId)
      .where("products.is_active", true)
      .leftJoin("featured_products", function () {
        this.on("featured_products.product_id", "=", "products.id").andOn(
          "featured_products.location_id",
          "=",
          "products.location_id"
        );
      })
      .select(
        "products.*",
        db.raw(
          "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
        ),
        db.raw("COALESCE(featured_products.is_top_pick, false) as is_top_pick"),
        db.raw("featured_products.sort_order as featured_sort_order"),
        db.raw("COALESCE(featured_products.active, false) as featured_active")
      );

    // Apply filters
    if (brandFilter) {
      baseQuery = baseQuery.where("products.brand_name", brandFilter);
    }
    if (categoryFilter.length > 0) {
      baseQuery = baseQuery.where((builder) => {
        builder.where("products.category", categoryFilter[0]);
        for (let i = 1; i < categoryFilter.length; i++) {
          builder.orWhere("products.category", categoryFilter[i]);
        }
      });
    }
    if (subcategoryFilter) {
      baseQuery = baseQuery.where("products.subcategory", subcategoryFilter);
    }

    // Apply price filters
    if (minPrice !== undefined) {
      baseQuery = baseQuery.where("products.latest_price", ">=", minPrice);
    }
    if (maxPrice !== undefined) {
      baseQuery = baseQuery.where("products.latest_price", "<=", maxPrice);
    }

    // Apply THC filters
    if (minThc !== undefined) {
      baseQuery = baseQuery.where("products.percentage_thc", ">=", minThc);
    }
    if (maxThc !== undefined) {
      baseQuery = baseQuery.where("products.percentage_thc", "<=", maxThc);
    }

    // Apply text search if provided
    if (searchQuery) {
      baseQuery = baseQuery.where((builder) => {
        builder
          .where("products.product_name", "like", `%${searchQuery}%`)
          .orWhere("products.raw_product_name", "like", `%${searchQuery}%`)
          .orWhere("products.brand_name", "like", `%${searchQuery}%`)
          .orWhere("products.category", "like", `%${searchQuery}%`)
          .orWhere("products.subcategory", "like", `%${searchQuery}%`)
          .orWhere("products.meta_sku", "like", `%${searchQuery}%`);
      });
    }

    let response;

    if (grouped) {
      // GROUPED PRODUCTS (DEFAULT)
      // Get all products that match filters
      const allProducts = await baseQuery.clone().orderBy("products.meta_sku");

      // Group products by meta_sku
      const groupedMap = new Map<string, any[]>();
      for (const product of allProducts) {
        if (!groupedMap.has(product.meta_sku)) {
          groupedMap.set(product.meta_sku, []);
        }
        groupedMap.get(product.meta_sku)!.push(product);
      }

      // Convert to grouped products array
      const groupedProducts: GroupedProduct[] = Array.from(
        groupedMap.entries()
      ).map(([metaSku, variants]) => {
        // Use the first variant as the base product for main details
        const baseProduct = variants[0];

        // Calculate price range
        const prices = variants
          .map((v) => v.latest_price || 0)
          .filter((p) => p > 0);
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
        const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

        // Create variant objects
        const productVariants: ProductVariant[] = variants.map((variant) => ({
          id: variant.id,
          product_id: variant.product_id,
          display_weight: variant.display_weight || "",
          latest_price: variant.latest_price || 0,
          medical: variant.medical || false,
          recreational: variant.recreational || false,
          external_id: variant.external_id,
          url: variant.url,
          inventory_quantity: variant.inventory_quantity,
          out_of_stock: variant.out_of_stock,
          slug: variant.slug,
        }));

        return {
          meta_sku: metaSku,
          product_name: baseProduct.product_name,
          brand_name: baseProduct.brand_name,
          category: baseProduct.category,
          subcategory: baseProduct.subcategory,
          description: baseProduct.product_description,
          image_url: baseProduct.image_url,
          product_tags: baseProduct.product_tags,
          percentage_thc: baseProduct.percentage_thc,
          percentage_cbd: baseProduct.percentage_cbd,
          rating: baseProduct.rating,
          reviews_count: baseProduct.reviews_count,
          variants: productVariants,
          base_price: minPrice,
          price_range: {
            min: minPrice,
            max: maxPrice,
          },
          mood: baseProduct.mood,
          effects: baseProduct.effects,
          product_description: baseProduct.product_description,
          short_description: baseProduct.short_description,
          inventory_quantity: baseProduct.inventory_quantity,
          out_of_stock: baseProduct.out_of_stock,
          slug: baseProduct.slug,
          // Featured product fields
          is_featured: baseProduct.is_featured || false,
          is_top_pick: baseProduct.is_top_pick || false,
          featured_sort_order: baseProduct.featured_sort_order,
          featured_active: baseProduct.featured_active || false,
        };
      });

      // Sort grouped products
      const validSortFields = [
        "product_name",
        "brand_name",
        "latest_price",
        "percentage_thc",
        "created_at",
      ];
      const sortField = validSortFields.includes(sort) ? sort : "created_at";
      const sortDirection = direction.toLowerCase() === "asc" ? 1 : -1;

      groupedProducts.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (sortField) {
          case "product_name":
            aValue = a.product_name?.toLowerCase() || "";
            bValue = b.product_name?.toLowerCase() || "";
            break;
          case "brand_name":
            aValue = a.brand_name?.toLowerCase() || "";
            bValue = b.brand_name?.toLowerCase() || "";
            break;
          case "latest_price":
            aValue = a.base_price || 0;
            bValue = b.base_price || 0;
            break;
          case "percentage_thc":
            aValue = a.percentage_thc || 0;
            bValue = b.percentage_thc || 0;
            break;
          default: // created_at - use first variant's created_at
            aValue = new Date(
              allProducts.find((p) => p.meta_sku === a.meta_sku)?.created_at ||
                0
            );
            bValue = new Date(
              allProducts.find((p) => p.meta_sku === b.meta_sku)?.created_at ||
                0
            );
        }

        if (aValue < bValue) return -1 * sortDirection;
        if (aValue > bValue) return 1 * sortDirection;
        return 0;
      });

      const total = groupedProducts.length;
      const paginatedProducts = groupedProducts.slice(
        offset,
        offset + pageSize
      );

      response = {
        products: paginatedProducts,
        pagination: {
          totalPages: Math.ceil(total / pageSize),
          currentPage: page,
          total,
          per_page: pageSize,
        },
      };

      logger.info({
        message: "PublicProductController: Grouped products retrieved",
        location_id: ctx.state.locationId,
        total_groups: total,
        returned_groups: paginatedProducts.length,
        filters: {
          brandFilter,
          categoryFilter,
          subcategoryFilter,
          searchQuery,
        },
        sort: sortParam,
        mapped_sort: sortField,
        direction,
      });
    } else {
      // INDIVIDUAL PRODUCTS (LEGACY)
      // Get total count (before pagination)
      const countQuery = baseQuery
        .clone()
        .clearSelect()
        .count("products.id as count");
      const [{ count }] = await countQuery;
      const total = parseInt(count as string);

      // Apply sorting
      const validSortFields = [
        "id",
        "product_name",
        "brand_name",
        "category",
        "latest_price",
        "percentage_thc",
        "created_at",
        "updated_at",
      ];
      const sortField = validSortFields.includes(sort) ? sort : "created_at";
      const sortDirection = direction.toLowerCase() === "asc" ? "asc" : "desc";
      baseQuery = baseQuery.orderBy(`products.${sortField}`, sortDirection);

      // Get paginated products
      const products = await baseQuery.limit(pageSize).offset(offset);

      response = {
        products,
        pagination: {
          totalPages: Math.ceil(total / pageSize),
          currentPage: page,
          total,
          per_page: pageSize,
        },
      };

      logger.info({
        message: "PublicProductController: Individual products retrieved",
        productCount: products.length,
        total,
        location_id: ctx.state.locationId,
        filters: {
          brandFilter,
          categoryFilter,
          subcategoryFilter,
          searchQuery,
        },
        sort: sortParam,
        mapped_sort: sortField,
        direction,
      });
    }

    // Log the final response structure
    logger.info({
      message: "PublicProductController: Response structure",
      responseKeys: Object.keys(response),
      productCount: response.products.length,
      pagination: response.pagination,
      location_id: ctx.state.locationId,
    });

    ctx.body = response;
  } catch (error) {
    // Log the error
    logger.error({
      message: "Error in PublicProductController GET /",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      query: ctx.query,
      location_id: ctx.state?.locationId,
    });

    // Send appropriate error response
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = { error: "Internal server error" };
    }
  }
});

/**
 * @swagger
 * /public/products/filters:
 *   get:
 *     summary: Get Available Product Filters
 *     description: Get all available filter options for brands, categories, and subcategories
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Available filter options
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 brands:
 *                   type: array
 *                   items:
 *                     type: string
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: string
 *                 subcategories:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: API key missing or invalid
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */
router.get("/filters", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Get distinct brands, categories, and subcategories
    const [brands, categories, subcategories] = await Promise.all([
      db("products")
        .where("location_id", ctx.state.locationId)
        .whereNotNull("brand_name")
        .where("brand_name", "!=", "")
        .distinct("brand_name")
        .orderBy("brand_name")
        .pluck("brand_name"),

      db("products")
        .where("location_id", ctx.state.locationId)
        .whereNotNull("category")
        .where("category", "!=", "")
        .distinct("category")
        .orderBy("category")
        .pluck("category"),

      db("products")
        .where("location_id", ctx.state.locationId)
        .whereNotNull("subcategory")
        .where("subcategory", "!=", "")
        .distinct("subcategory")
        .orderBy("subcategory")
        .pluck("subcategory"),
    ]);

    const response: FilterOptions = {
      brands: brands || [],
      categories: categories || [],
      subcategories: subcategories || [],
    };

    logger.info({
      message: "PublicProductController: Filters retrieved",
      location_id: ctx.state.locationId,
      brands_count: response.brands.length,
      categories_count: response.categories.length,
      subcategories_count: response.subcategories.length,
    });

    ctx.body = response;
  } catch (error) {
    logger.error({
      message: "Error in PublicProductController GET /filters",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      location_id: ctx.state?.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = { error: "Internal server error" };
    }
  }
});

// Process product data to ensure location_id and retailer_id are correctly set
const processProductData = (
  data: any[],
  locationId: number,
  retailerId?: string
): any[] => {
  return data.map((product) => ({
    ...product,
    location_id: locationId, // Always override with the API key's location_id
    retailer_id: retailerId || product.retailer_id, // Override retailer_id if available from location
  }));
};

/**
 * @swagger
 * /public/products/upload:
 *   post:
 *     summary: Upload Public Products
 *     description: Upload product data for a location (API key required)
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ProductUploadRequest'
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               reindex:
 *                 type: boolean
 *               enhance_with_ai:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Product data imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ProductUploadResponse'
 *       401:
 *         description: API key missing or invalid
 *       400:
 *         description: Failed to import product data
 *       500:
 *         description: Internal server error
 */
router.post("/upload", async (ctx) => {
  if (!ctx.state.locationId || !ctx.state.location) {
    throw new RequestError("Location not found", 404);
  }

  const locationId = ctx.state.locationId;
  const retailerId = ctx.state.location.retailer_id;

  // Log retailer ID if available
  if (retailerId) {
    logger.info(`Using retailer ID from location: ${retailerId}`);
  } else {
    logger.info(`No retailer ID configured for location ${locationId}`);
  }

  // Get reindex and enhance flags from query params or body
  const shouldReindex =
    ctx.request.query.reindex === "true" || ctx.request.body?.reindex === true;
  const enhanceWithAI =
    ctx.request.query.enhance_with_ai === "true" ||
    ctx.request.body?.enhance_with_ai === true;

  // Check if this is a direct JSON data import
  if (ctx.request.body?.product_data) {
    try {
      // Process product data to override location_id and retailer_id
      const productsData = Array.isArray(ctx.request.body.product_data)
        ? ctx.request.body.product_data
        : [ctx.request.body.product_data];

      // Apply location_id and retailer_id overrides
      const processedData = processProductData(
        productsData,
        locationId,
        retailerId
      );

      // Create normalized data
      const normalizedData: NormalizedData = {
        type: "product",
        data: processedData,
        errors: [],
      };

      // Import the normalized data
      const result = await importProducts({
        location_id: locationId,
        normalization_data: normalizedData,
        enhance_with_ai: enhanceWithAI,
        reindex: shouldReindex,
      });

      ctx.status = 200;
      ctx.body = {
        message: "Product data imported successfully",
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to import product data",
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    // For file uploads, location_id will be applied during import
    // We can now pass retailer_id to be applied to all products

    const result = await importProducts({
      location_id: locationId,
      stream,
      enhance_with_ai: enhanceWithAI,
      reindex: shouldReindex,
      retailer_id: retailerId,
    });

    ctx.status = 200;
    ctx.body = {
      message: "Products imported successfully",
      ...result,
    };
  } catch (error) {
    logger.error("Error uploading products:", error);
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error
          ? error.message
          : "Failed to import product data",
    };
  }
});

/**
 * @swagger
 * /public/products/chat:
 *   post:
 *     summary: Chat with Smokey for Product Recommendations
 *     description: |
 *       Chat with Smokey AI for product recommendations. Requires location API key for access.
 *
 *       **Authentication Options:**
 *       - **API Key Only (Anonymous)**: Just provide the API key in Authorization header. Chat won't be saved.
 *       - **API Key + User Token (Authenticated)**: Provide both headers for full functionality including chat history.
 *
 *       **Headers Required:**
 *       - `Authorization: Bearer your_location_api_key` (Required)
 *       - `x-user-token: user_jwt_token` (Optional - for chat persistence)
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChatMessageRequest'
 *     responses:
 *       200:
 *         description: Smokey's response with product recommendations
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChatMessageResponse'
 *       401:
 *         description: API key missing or invalid
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
router.post("/chat", async (ctx) => {
  if (!ctx.state.locationId || !ctx.state.location) {
    throw new RequestError("Location not found", 404);
  }

  const locationId = ctx.state.locationId;

  try {
    // Validate request body
    const payload = validate<ChatMessageParams>(
      chatMessageSchema,
      ctx.request.body
    );

    if (!payload.message || payload.message.trim() === "") {
      throw new RequestError("Message content cannot be empty", 400);
    }

    // Check for optional user authentication (for chat history)
    let userId: string | null = null;
    let isAuthenticated = false;

    // Look for user token in headers (optional)
    const userAuthHeader = ctx.request.headers["x-user-token"] as string;
    if (userAuthHeader) {
      try {
        // Here you would validate the user token and get user ID
        // For now, we'll just extract it directly (implement proper JWT validation)
        userId = userAuthHeader;
        isAuthenticated = true;
        logger.info({
          message: "User authenticated for chat",
          userId: userId?.substring(0, 8) + "...", // Log partial for privacy
          locationId,
        });
      } catch (error) {
        logger.warn({
          message: "Invalid user token provided, continuing as anonymous",
          error: error instanceof Error ? error.message : "Unknown error",
        });
        // Continue as anonymous user
      }
    }

    // Get the app instance
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Initialize services
    let wss = app.getWebSocketServer();
    if (!wss) {
      // Create minimal implementation for public controller
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);
    const chatAIService = app.chatAIService;

    if (!chatAIService) {
      throw new RequestError("AI service not available", 500);
    }

    // Get or create chat
    let chat;
    if (payload.chat_id && isAuthenticated) {
      // Only allow chat retrieval for authenticated users
      try {
        chat = await chatService.getChat(payload.chat_id, locationId);
        if (!chat) {
          logger.warn({
            message: "Chat not found for authenticated user",
            chatId: payload.chat_id,
            userId,
            locationId,
          });
          throw new RequestError("Chat not found", 404);
        }
      } catch (error) {
        logger.error({
          message: "Error retrieving chat for authenticated user",
          error: error instanceof Error ? error.message : "Unknown error",
          chatId: payload.chat_id,
          userId,
        });
        throw new RequestError("Error retrieving chat", 500);
      }
    }

    if (!chat) {
      // Create a new chat - use session-based ID for anonymous users
      const chatName = isAuthenticated
        ? "Product Recommendation Chat"
        : `Anonymous Chat ${Date.now()}`;

      // For anonymous users, we create a temporary chat that won't persist long-term
      chat = {
        id: Date.now().toString(),
        chat_id: `anon_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        name: chatName,
        created_at: new Date(),
        updated_at: new Date(),
        location_id: locationId,
        agent_ids: "1", // Smokey agent ID
        status: "active",
        metadata: {
          isAnonymous: !isAuthenticated,
          userId,
        },
        agents: [
          {
            id: "1",
            name: "SMOKEY",
            role: "AI Budtender & Customer Experience",
            description: "Product recommendation specialist",
            icon: "🌿",
            capabilities: ["Product recommendations", "Cannabis education"],
            disabled: false,
            metadata: {},
          },
        ],
      };

      // Only persist chat for authenticated users
      if (isAuthenticated) {
        try {
          chat = await chatService.createChat(
            locationId,
            chatName,
            chat.agents[0]
          );
        } catch (error) {
          logger.error({
            message: "Error creating chat for authenticated user",
            error: error instanceof Error ? error.message : "Unknown error",
            userId,
          });
          // Continue with temporary chat
        }
      }
    }

    // Analyze intent to determine response type
    const intent = await chatAIService.analyzeIntent(payload.message.trim());

    logger.info({
      message: "Intent analysis for public chat",
      intent: intent.intent,
      confidence: intent.confidence,
      chatId: chat.chat_id,
      isAuthenticated,
    });

    // Create user message (only persist for authenticated users)
    const userMessage: Omit<Message, "id"> = {
      content: payload.message.trim(),
      role: "user",
      timestamp: new Date(),
      chat_id: chat.chat_id,
      metadata: {
        isAnonymous: !isAuthenticated,
        userId,
        intent: intent.intent,
        confidence: intent.confidence,
      },
    };

    let savedUserMessage;
    if (isAuthenticated) {
      try {
        savedUserMessage = await chatService.createMessage(userMessage);
      } catch (error) {
        logger.error({
          message: "Error saving user message for authenticated user",
          error: error instanceof Error ? error.message : "Unknown error",
        });
        // Continue without saving
        savedUserMessage = { ...userMessage, id: Date.now() };
      }
    } else {
      // For anonymous users, just create a temporary message object
      savedUserMessage = { ...userMessage, id: Date.now() };
    }

    // Handle response based on intent
    let textResponse: string;
    let suggestedQuestions: string[] = [];
    let processedProducts: any[] = [];
    let responseData: any = {};

    if (intent.intent === "product_recommendation") {
      // Use ProductRecommendationTool for structured product recommendations
      logger.info({
        message: "Using ProductRecommendationTool for product recommendations",
        query: userMessage.content,
        locationId,
        isAuthenticated,
      });

      try {
        // Import and use the ProductRecommendationTool
        const { ProductRecommendationTool } = await import(
          "../tools/ProductRecommendationTool"
        );
        const productTool = new ProductRecommendationTool();

        // Get product recommendations
        const toolResult = await productTool.execute(
          {
            query: userMessage.content,
            topK: 8,
          },
          { locationId }
        );

        if (toolResult.status !== "success") {
          throw new Error(toolResult.error || "Product recommendation failed");
        }

        const productRecommendations =
          (toolResult.data as any)?.recommendations || [];

        if (productRecommendations.length === 0) {
          textResponse =
            "I'm sorry, but I couldn't find any products matching your request. Could you try describing what you're looking for differently?";
          suggestedQuestions = [
            "What kind of effects are you looking for?",
            "Are you interested in flower, edibles, or concentrates?",
            "What's your preferred THC or CBD content?",
          ];
        } else {
          // Generate contextual response
          const topProducts = productRecommendations.slice(0, 3);
          const productNames = topProducts
            .map((p: Product) => p.product_name)
            .join(", ");

          textResponse =
            `Great! I found ${productRecommendations.length} products that match your request. ` +
            `Some top recommendations include ${productNames}. ` +
            `Each of these products has been selected based on your specific needs and preferences. ` +
            `Feel free to ask about any specific product for more details!`;

          suggestedQuestions = [
            "Can you tell me more about the effects of these products?",
            "What's the difference between these recommendations?",
            "Do you have any lower or higher THC options?",
          ];
        }
        console.log(
          "productRecommendations $$",
          productRecommendations.slice(0, 2)
        );
        // Process products for frontend display
        processedProducts = productRecommendations.map((product: any) => ({
          product_id: product.product_id || product.metadata.product_id,
          id: product.id || product.metadata.source_id,
          meta_sku: product.meta_sku,
          product_name: product.product_name || "Unknown product",
          description: product.description || "No description available",
          category: product.category || "Uncategorized",
          subcategory: product.subcategory || "",
          brand_name: product.brand_name || "Unknown brand",
          price: product.price,
          latest_price: product.latest_price,
          thc_percentage: product.thc_percentage || product.thc || "N/A",
          cbd_percentage: product.cbd_percentage || product.cbd || "N/A",
          image_url:
            product.image_url ||
            "https://placehold.co/300x300/65715F/FFFFFF?text=Product",
          weight: product.weight || "",
          display_weight: product.display_weight || product.weight || "",
          rating: product.rating,
          reviews_count: product.reviews_count,
          recommendation_reason: product.recommendation_reason,
          score: product.score,
          rank: product.rank,
        }));

        responseData = {
          type: "product_recommendations",
          products: processedProducts,
          total_found: productRecommendations.length,
          search_query: userMessage.content,
          using_tool: "ProductRecommendationTool",
        };

        logger.info({
          message: "Successfully generated product recommendations",
          productCount: processedProducts.length,
          suggestedQuestionsCount: suggestedQuestions.length,
          isAuthenticated,
          chatId: chat.chat_id,
        });
      } catch (toolError) {
        logger.error({
          message: "Error using ProductRecommendationTool",
          error:
            toolError instanceof Error ? toolError.message : "Unknown error",
          query: userMessage.content,
          locationId,
        });

        // Fallback to general LLM response
        textResponse =
          "I'm having trouble accessing the product database right now. Let me try to help you with general information instead.";
        suggestedQuestions = [
          "What kind of products are you looking for?",
          "Can you be more specific about your needs?",
          "Are you interested in a particular category?",
        ];
        responseData = {
          type: "general_response",
          error: "Product search temporarily unavailable",
        };
      }
    } else {
      // For general inquiries, use SMOKEY directly without complex tool routing
      logger.info({
        message: "Using direct SMOKEY response for non-product inquiry",
        intent: intent.intent,
        query: userMessage.content,
        locationId,
        isAuthenticated,
      });

      try {
        // Build system prompt for SMOKEY
        const systemPrompt = await buildSmokeySystemPrompt(locationId);

        // Get chat history for context
        const chatHistory = await chatService.getMessages(chat.chat_id, 10);
        const formattedHistory = chatHistory.map((msg) => ({
          role: msg.role,
          content: msg.content,
        }));

        // Generate AI response using the direct SMOKEY method (no duplicate intent analysis)
        textResponse = await chatAIService.generateSmokeyResponse(
          systemPrompt,
          userMessage.content,
          formattedHistory
        );

        responseData = {
          type: "general_response",
          using_service: "SmokeyAIService.generateSmokeyResponse",
        };

        // Generate appropriate follow-up questions based on intent
        switch (intent.intent) {
          case "general_cannabis_question":
            suggestedQuestions = [
              "Do you have any other cannabis questions?",
              "Would you like product recommendations?",
              "Can I help you find something specific?",
            ];
            break;
          case "location_inquiry":
            suggestedQuestions = [
              "Do you need directions to our location?",
              "What are our current hours?",
              "Would you like to see our products?",
            ];
            break;
          default:
            suggestedQuestions = [
              "Is there anything else I can help you with?",
              "Would you like to see our product recommendations?",
              "Do you have any questions about cannabis?",
            ];
        }

        logger.info({
          message: "Successfully generated direct SMOKEY response",
          intent: intent.intent,
          responseLength: textResponse.length,
          isAuthenticated,
          chatId: chat.chat_id,
        });
      } catch (llmError) {
        logger.error({
          message: "Error using direct SMOKEY response",
          error: llmError instanceof Error ? llmError.message : "Unknown error",
          query: userMessage.content,
          locationId,
        });

        // Final fallback response
        textResponse =
          "I'm sorry, I'm having trouble processing your request right now. Please try again in a moment.";
        suggestedQuestions = [
          "Can you try rephrasing your question?",
          "Would you like to browse our products?",
          "Is there something specific I can help you find?",
        ];
        responseData = {
          type: "error_response",
          error: "AI service temporarily unavailable",
        };
      }
    }

    // Create AI response message
    const aiMessage: Omit<Message, "id"> = {
      content: textResponse,
      role: "assistant",
      timestamp: new Date(),
      chat_id: chat.chat_id,
      agent_id: "1", // Smokey
      metadata: {
        isAnonymous: !isAuthenticated,
        intent: intent.intent,
        confidence: intent.confidence,
        data: responseData,
      },
    };

    // Save AI message only for authenticated users
    let savedAiMessage;
    if (isAuthenticated) {
      try {
        savedAiMessage = await chatService.createMessage(aiMessage);
      } catch (error) {
        logger.error({
          message: "Error saving AI message for authenticated user",
          error: error instanceof Error ? error.message : "Unknown error",
        });
        savedAiMessage = { ...aiMessage, id: Date.now() + 1 };
      }
    } else {
      savedAiMessage = { ...aiMessage, id: Date.now() + 1 };
    }

    // Return structured response
    ctx.body = {
      type: "ai",
      content: textResponse,
      message_id: savedAiMessage.id.toString(),
      chat_id: chat.chat_id,
      data: {
        suggested_next_questions: suggestedQuestions,
        products: processedProducts,
        search_metadata: {
          intent: intent.intent,
          confidence: intent.confidence,
          total_found: processedProducts.length,
          search_query: userMessage.content,
          is_authenticated: isAuthenticated,
          response_type: responseData.type || "unknown",
          ...responseData,
        },
      },
    };
  } catch (error) {
    logger.error({
      message: "Error in public chat endpoint",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      type: "ai",
      content:
        error instanceof Error ? error.message : "An unknown error occurred",
      message_id: Date.now().toString(),
      error: true,
    };
  }
});

/**
 * @swagger
 * /public/products/search:
 *   get:
 *     summary: Search Products Directly
 *     description: Search products without chat interface using semantic search
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           maximum: 50
 *         description: Number of products to return
 *     responses:
 *       200:
 *         description: Product search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 search_metadata:
 *                   type: object
 *                   properties:
 *                     query:
 *                       type: string
 *                     total_found:
 *                       type: integer
 *                     search_method:
 *                       type: string
 *       400:
 *         description: Missing or invalid search query
 *       500:
 *         description: Internal server error
 */
router.get("/search", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  const query = ctx.query.q as string;
  if (!query || query.trim() === "") {
    throw new RequestError("Search query (q) is required", 400);
  }

  const limit = Math.min(parseInt((ctx.query.limit as string) || "10", 10), 50);

  try {
    logger.info({
      message: "Direct product search request",
      query: query.trim(),
      limit,
      locationId: ctx.state.locationId,
    });

    // Use ProductRecommendationTool for consistent search
    const { ProductRecommendationTool } = await import(
      "../tools/ProductRecommendationTool"
    );
    const productTool = new ProductRecommendationTool();

    const toolResult = await productTool.execute(
      {
        query: query.trim(),
        topK: limit,
      },
      { locationId: ctx.state.locationId }
    );

    if (toolResult.status !== "success") {
      throw new Error(toolResult.error || "Product search failed");
    }

    const recommendations = (toolResult.data as any)?.recommendations || [];

    // Format products for direct search response (simpler than chat)
    const products = recommendations.map((product: any) => ({
      product_id: product.meta_sku || product.id,
      product_name: product.product_name,
      description: product.description,
      category: product.category,
      subcategory: product.subcategory,
      brand_name: product.brand_name,
      price: product.price,
      latest_price: product.latest_price,
      thc_percentage: product.thc_percentage,
      cbd_percentage: product.cbd_percentage,
      image_url: product.image_url,
      weight: product.weight,
      display_weight: product.display_weight,
      rating: product.rating,
      reviews_count: product.reviews_count,
      match_score: product.score,
    }));

    ctx.body = {
      products,
      search_metadata: {
        query: query.trim(),
        total_found: products.length,
        search_method: "semantic_vector_search",
        location_id: ctx.state.locationId,
      },
    };
  } catch (error) {
    logger.error({
      message: "Error in direct product search",
      error: error instanceof Error ? error.message : "Unknown error",
      query,
      locationId: ctx.state.locationId,
    });

    ctx.status = 500;
    ctx.body = {
      error: error instanceof Error ? error.message : "Product search failed",
      products: [],
    };
  }
});

/**
 * @swagger
 * /public/products/status:
 *   get:
 *     summary: API Status Check
 *     description: Check if the public API is working and get location info
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: API status and location information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 location:
 *                   type: object
 *                 smokey_available:
 *                   type: boolean
 *                 product_search_available:
 *                   type: boolean
 *       401:
 *         description: API key missing or invalid
 *       500:
 *         description: Internal server error
 */
router.get("/status", async (ctx) => {
  if (!ctx.state.locationId || !ctx.state.location) {
    throw new RequestError("Location not found", 404);
  }

  try {
    // Test ProductRecommendationTool availability
    let productSearchAvailable = false;
    try {
      const { ProductRecommendationTool } = await import(
        "../tools/ProductRecommendationTool"
      );
      const productTool = new ProductRecommendationTool();
      // Quick test with a simple query
      const testResult = await productTool.execute(
        { query: "test", topK: 1 },
        { locationId: ctx.state.locationId }
      );
      productSearchAvailable = testResult.status === "success";
    } catch (error) {
      logger.warn({
        message: "ProductRecommendationTool not available in status check",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    ctx.body = {
      status: "operational",
      location: {
        id: ctx.state.location.id,
        name: ctx.state.location.name,
        city: ctx.state.location.city,
        state: ctx.state.location.state,
        retailer_id: ctx.state.location.retailer_id,
      },
      smokey_available: true,
      product_search_available: productSearchAvailable,
      endpoints: {
        chat: "/public/products/chat",
        chats: "/public/products/chats (requires user auth)",
        create_chat: "POST /public/products/chats (requires user auth)",
        chat_details: "/public/products/chats/{id} (requires user auth)",
        update_chat: "PUT /public/products/chats/{id} (requires user auth)",
        delete_chat: "DELETE /public/products/chats/{id} (requires user auth)",
        archive_chat:
          "POST /public/products/chats/{id}/archive (requires user auth)",
        chat_messages:
          "/public/products/chats/{id}/messages (requires user auth)",
        chat_metadata:
          "PUT /public/products/chats/{id}/metadata (requires user auth)",
        available_agents: "/public/products/chats/agents",
        generate_plan: "POST /public/products/chats/generate-plan",
        upload_file: "POST /public/products/chats/uploads (requires user auth)",
        get_file: "/public/products/chats/uploads/{id}",
        search: "/public/products/search",
        products:
          "/public/products (grouped by metaSKU by default, use ?grouped=false for individual)",
        filters: "/public/products/filters (get available filter options)",
        upload: "/public/products/upload",
        create_vip_user: "POST /public/products/users (create VIP user)",
      },
      authentication: {
        api_key_required: true,
        user_auth_optional: true,
        user_auth_header: "x-user-token",
      },
    };
  } catch (error) {
    logger.error({
      message: "Error in status endpoint",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
    });

    ctx.status = 500;
    ctx.body = {
      status: "error",
      error: error instanceof Error ? error.message : "Status check failed",
    };
  }
});

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 *       description: Location API key in the format 'Bearer YOUR_API_KEY'. Required for all endpoints.
 *     UserAuth:
 *       type: apiKey
 *       in: header
 *       name: x-user-token
 *       description: Optional user authentication token for enhanced features like chat history persistence. Use alongside ApiKeyAuth.
 *   examples:
 *     DualAuthentication:
 *       summary: Using both API key and user authentication
 *       description: Example of headers when using both API key (required) and user authentication (optional)
 *       value:
 *         Authorization: "Bearer your_location_api_key_here"
 *         x-user-token: "user_jwt_token_here"
 *     ApiKeyOnly:
 *       summary: Using only API key (anonymous access)
 *       description: Example of headers when using only API key for anonymous access
 *       value:
 *         Authorization: "Bearer your_location_api_key_here"
 */

function convertToChat(dbChat: any): any {
  return {
    id: dbChat.id.toString(),
    chat_id: dbChat.chat_id,
    name: dbChat.name,
    created_at: dbChat.created_at,
    updated_at: dbChat.updated_at,
    location_id: dbChat.location_id.toString(),
    agent_ids: dbChat.agent_ids,
    status: dbChat.status,
    metadata: dbChat.metadata || {},
    agents: dbChat.agents?.map((agent: any) => ({
      id: agent.id,
      name: agent.name,
      role: agent.role,
      description: agent.description,
      icon: agent.icon || "🤖",
      capabilities: agent.capabilities,
      disabled: agent.disabled,
      metadata: agent.metadata || {},
    })),
  };
}

/**
 * @swagger
 * /public/products/chats:
 *   get:
 *     summary: List Chats (Authenticated Users Only)
 *     description: Retrieve list of chats for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     responses:
 *       200:
 *         description: List of chats retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   chat_id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   created_at:
 *                     type: string
 *                   updated_at:
 *                     type: string
 *                   location_id:
 *                     type: string
 *                   status:
 *                     type: string
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create New Chat (Authenticated Users Only)
 *     description: Create a new chat for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *               - agent_id
 *             properties:
 *               message:
 *                 type: string
 *               agent_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Chat created successfully
 *       401:
 *         description: Authentication required
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal server error
 */
router.get("/chats", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required for chat list)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required for chat list", 401);
  }

  try {
    const userId = userAuthHeader; // In production, validate JWT token
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get user's chats for this location (matching ChatController format)
    const chats = await chatService.listChats(ctx.state.locationId);

    // Filter chats for this user
    const userChats = chats.filter((chat) => {
      const metadata = chat.metadata || {};
      return metadata.userId === userId;
    });

    ctx.body = userChats.map((chat) => convertToChat(chat));
  } catch (error) {
    logger.error({
      message: "Error retrieving chats",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error retrieving chats",
    };
  }
});

// Create new chat endpoint
router.post("/chats", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const payload = validate<{
      message: string;
      agent_id: string;
    }>(
      {
        type: "object",
        required: ["message", "agent_id"],
        properties: {
          message: { type: "string" },
          agent_id: { type: "string" },
        },
        additionalProperties: false,
      },
      ctx.request.body
    );

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Create Smokey agent (for now, we'll use a default agent)
    const agent = {
      id: "1",
      name: "SMOKEY",
      role: "AI Budtender & Customer Experience",
      description: "Product recommendation specialist",
      icon: "🌿",
      capabilities: ["Product recommendations", "Cannabis education"],
      disabled: false,
      metadata: { userId },
      unlocked: true,
      missingRequirements: [],
    };

    const chat = await chatService.createChat(
      ctx.state.locationId,
      "New Chat",
      agent
    );

    // Update chat metadata to include user ID
    await chatService.updateChatMetadata(chat.chat_id, {
      userId,
      isAnonymous: false,
    });

    ctx.body = convertToChat(chat);
  } catch (error) {
    logger.error({
      message: "Error creating chat",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error creating chat",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/{id}:
 *   get:
 *     summary: Get Chat Details (Authenticated Users Only)
 *     description: Retrieve details of a specific chat for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     responses:
 *       200:
 *         description: Chat details retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update/Rename Chat (Authenticated Users Only)
 *     description: Update chat name for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: Chat updated successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete Chat (Authenticated Users Only)
 *     description: Delete a chat for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     responses:
 *       204:
 *         description: Chat deleted successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
router.get("/chats/:id", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get chat and verify it belongs to the user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const metadata = chat.metadata || {};
    if (metadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    ctx.body = convertToChat(chat);
  } catch (error) {
    logger.error({
      message: "Error retrieving chat details",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error retrieving chat",
    };
  }
});

// Update/rename chat endpoint
router.put("/chats/:id", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const { name } = ctx.request.body;

    if (!name || typeof name !== "string") {
      throw new RequestError("Chat name is required", 400);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get chat and verify it belongs to the user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const metadata = chat.metadata || {};
    if (metadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    // Rename the chat
    await chatService.renameChat(chatId, name, ctx.state.locationId);

    // Mark as manually renamed
    const newMetadata = {
      ...metadata,
      wasRenamed: true,
    };
    await chatService.updateChatMetadata(chat.chat_id, newMetadata);

    // Get updated chat
    const updatedChat = await chatService.getChat(chatId, ctx.state.locationId);
    ctx.body = convertToChat(updatedChat!);
  } catch (error) {
    logger.error({
      message: "Error updating chat",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error updating chat",
    };
  }
});

// Delete chat endpoint
router.delete("/chats/:id", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get chat and verify it belongs to the user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const metadata = chat.metadata || {};
    if (metadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    // Delete the chat
    await chatService.deleteChat(chatId, ctx.state.locationId);
    ctx.status = 204;
  } catch (error) {
    logger.error({
      message: "Error deleting chat",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error deleting chat",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/{id}/archive:
 *   post:
 *     summary: Archive Chat (Authenticated Users Only)
 *     description: Archive a chat for authenticated users
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     responses:
 *       204:
 *         description: Chat archived successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
router.post("/chats/:id/archive", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get chat and verify it belongs to the user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const metadata = chat.metadata || {};
    if (metadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    // Archive the chat
    await chatService.archiveChat(chatId, ctx.state.locationId);
    ctx.status = 204;
  } catch (error) {
    logger.error({
      message: "Error archiving chat",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error archiving chat",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/{id}/messages:
 *   get:
 *     summary: Get Chat Messages (Authenticated Users Only)
 *     description: Retrieve messages for a specific chat
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *       - in: query
 *         name: before
 *         schema:
 *           type: string
 *         description: Get messages before this timestamp
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of messages to return
 *     responses:
 *       200:
 *         description: Messages retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       500:
 *         description: Internal server error
 */
router.get("/chats/:id/messages", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const { before, limit = "50" } = ctx.query;
    const beforeDate = before ? new Date(before as string) : undefined;
    const limitNum = parseInt(limit as string, 10);

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Verify chat exists and belongs to user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const metadata = chat.metadata || {};
    if (metadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    // Get messages (matching ChatController format exactly)
    const messages = await chatService.getMessages(
      chatId,
      limitNum,
      beforeDate
    );
    ctx.body = messages;
  } catch (error) {
    logger.error({
      message: "Error retrieving chat messages",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error:
        error instanceof Error ? error.message : "Error retrieving messages",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/agents:
 *   get:
 *     summary: Get Available Agents
 *     description: Get list of available chat agents for the location
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Available agents retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   role:
 *                     type: string
 *                   description:
 *                     type: string
 *                   icon:
 *                     type: string
 *                   capabilities:
 *                     type: array
 *                     items:
 *                       type: string
 *                   unlocked:
 *                     type: boolean
 *       500:
 *         description: Internal server error
 */
router.get("/chats/agents", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  try {
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Get available agents for this location
    const agents = await chatService.getAvailableAgents(ctx.state.locationId);

    // For public API, we'll return simplified agent info
    const publicAgents = agents.map((agent) => ({
      id: agent.id,
      name: agent.name,
      role: agent.role,
      description: agent.description,
      icon: agent.icon || "🤖",
      capabilities: agent.capabilities,
      unlocked: true, // For public API, assume all agents are unlocked
      disabled: agent.disabled || false,
    }));

    ctx.body = publicAgents;
  } catch (error) {
    logger.error({
      message: "Error retrieving available agents",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
    });

    ctx.status = 500;
    ctx.body = {
      error: error instanceof Error ? error.message : "Error retrieving agents",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/{id}/metadata:
 *   put:
 *     summary: Update Chat Metadata (Authenticated Users Only)
 *     description: Update metadata for a specific chat
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Chat ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties: true
 *     responses:
 *       200:
 *         description: Metadata updated successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Chat not found
 *       400:
 *         description: Invalid metadata
 *       500:
 *         description: Internal server error
 */
router.put("/chats/:id/metadata", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const chatId = ctx.params.id;
    const metadata = ctx.request.body;

    if (!chatId || typeof metadata !== "object") {
      throw new RequestError("Invalid chat ID or metadata", 400);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let wss = app.getWebSocketServer();
    if (!wss) {
      wss = {
        clients: new Set(),
        on: () => {},
        off: () => {},
      } as any;
    }

    const chatService = new ChatService(db, wss as any);

    // Verify chat exists and belongs to user
    const chat = await chatService.getChat(chatId, ctx.state.locationId);
    if (!chat || chat.location_id !== ctx.state.locationId) {
      throw new RequestError("Chat not found", 404);
    }

    // Verify this chat belongs to the authenticated user
    const existingMetadata = chat.metadata || {};
    if (existingMetadata.userId !== userId) {
      throw new RequestError("Chat not found", 404);
    }

    // Update metadata (preserve userId)
    const updatedMetadata = {
      ...metadata,
      userId, // Always preserve the userId
    };

    await chatService.updateChatMetadata(chatId, updatedMetadata);

    ctx.status = 200;
    ctx.body = { success: true };
  } catch (error) {
    logger.error({
      message: "Error updating chat metadata",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      chatId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Error updating metadata",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/generate-plan:
 *   post:
 *     summary: Generate Marketing Plan from Chat
 *     description: Generate an automation plan based on chat message
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *               chat_id:
 *                 type: string
 *                 nullable: true
 *     responses:
 *       200:
 *         description: Plan generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 plan:
 *                   type: object
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal server error
 */
router.post("/chats/generate-plan", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  try {
    const { message, chat_id } = ctx.request.body;

    if (!message || typeof message !== "string") {
      throw new RequestError("Message is required", 400);
    }

    // Import CrewAI service
    const { crewAIService } = await import("../insights/CrewAIService");

    // Generate the automation plan using the location ID and message
    const plan = await crewAIService.handleRequest(
      ctx.state.locationId,
      message
    );

    ctx.body = {
      success: true,
      plan,
      message: "Automation plan generated successfully",
    };
  } catch (error) {
    logger.error({
      message: "Error generating automation plan from chat",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
      user_query: ctx.request.body?.message,
    });

    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "Failed to generate automation plan",
      error: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /public/products/chats/uploads:
 *   post:
 *     summary: Upload File to Chat (Authenticated Users Only)
 *     description: Upload a file for use in chat conversations
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               chat_id:
 *                 type: string
 *                 nullable: true
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 name:
 *                   type: string
 *                 type:
 *                   type: string
 *                 size:
 *                   type: integer
 *                 url:
 *                   type: string
 *       400:
 *         description: No file uploaded or invalid request
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Internal server error
 */
router.post("/chats/uploads", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  // Check for user authentication (required)
  const userAuthHeader = ctx.request.headers["x-user-token"] as string;
  if (!userAuthHeader) {
    throw new RequestError("User authentication required", 401);
  }

  try {
    const userId = userAuthHeader;
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Import required modules
    const fs = await import("fs");
    const path = await import("path");
    const crypto = await import("crypto");
    const util = await import("util");

    const mkdirp = util.promisify(fs.mkdir);
    const writeFile = util.promisify(fs.writeFile);
    const fsAccess = util.promisify(fs.access);

    // Get the uploaded file from the request
    const file = ctx.request.files?.file;

    if (!file) {
      throw new RequestError("No file uploaded", 400);
    }

    // Extract chat_id from the request
    const chatId = ctx.request.body?.chat_id;

    // Validate chat belongs to user if chat_id is provided
    if (chatId) {
      let wss = app.getWebSocketServer();
      if (!wss) {
        wss = {
          clients: new Set(),
          on: () => {},
          off: () => {},
        } as any;
      }

      const chatService = new ChatService(db, wss as any);
      const chat = await chatService.getChat(chatId, ctx.state.locationId);
      if (!chat) {
        throw new RequestError("Chat not found", 404);
      }

      // Verify this chat belongs to the authenticated user
      const metadata = chat.metadata || {};
      if (metadata.userId !== userId) {
        throw new RequestError("Chat not found", 404);
      }
    }

    // Generate a unique filename
    const uniqueId = crypto.randomUUID();

    // Extract file information
    const originalFile = Array.isArray(file) ? file[0] : file;
    const originalName = originalFile.originalFilename || uniqueId;
    const contentType = originalFile.mimetype;
    const fileSize = originalFile.size;

    // Read the file content
    const fileContent = fs.readFileSync(originalFile.filepath);

    // Define upload directory structure
    const uploadBaseDir = path.join(process.cwd(), "uploads");
    const locationDir = path.join(
      uploadBaseDir,
      ctx.state.locationId.toString()
    );
    const chatDir = chatId ? path.join(locationDir, chatId) : locationDir;

    // Create directory if it doesn't exist
    try {
      await fsAccess(chatDir);
    } catch (error) {
      await mkdirp(chatDir, { recursive: true });
    }

    // Generate a safe filename
    const fileExtension = path.extname(originalName);
    const safeFileName = `${uniqueId}${fileExtension}`;
    const filePath = path.join(chatDir, safeFileName);

    // Save the file
    await writeFile(filePath, fileContent);

    // Save file metadata to database
    const fileMetadata = {
      original_name: originalName,
      content_type: contentType,
      size: fileSize,
      path: filePath,
      location_id: ctx.state.locationId,
      chat_id: chatId || null,
      uploaded_at: new Date(),
      uploaded_by: userId,
    };

    // Insert the record first
    await db("chat_attachments").insert(fileMetadata);

    // Get the last inserted ID
    const result = await db.raw("SELECT LAST_INSERT_ID() as id");
    let insertedId;
    if (Array.isArray(result) && result.length > 0) {
      insertedId = result[0][0]?.id;
    } else if (result && typeof result === "object") {
      insertedId = Array.isArray(result) ? result[0]?.id : result.id;
    }

    if (!insertedId) {
      throw new Error("Failed to retrieve the last inserted ID");
    }

    // Generate a URL for the uploaded file
    const fileUrl = `/public/products/chats/uploads/${insertedId}`;

    ctx.status = 200;
    ctx.body = {
      id: insertedId,
      name: originalName,
      type: contentType,
      size: fileSize,
      url: fileUrl,
    };
  } catch (error) {
    logger.error({
      message: "File upload error",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "File upload failed",
    };
  }
});

/**
 * @swagger
 * /public/products/chats/uploads/{id}:
 *   get:
 *     summary: Get Uploaded File
 *     description: Retrieve an uploaded file by ID
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: File ID
 *     responses:
 *       200:
 *         description: File content
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid file ID
 *       403:
 *         description: Access denied
 *       404:
 *         description: File not found
 *       500:
 *         description: Internal server error
 */
router.get("/chats/uploads/:id", async (ctx) => {
  if (!ctx.state.locationId) {
    throw new RequestError("Location not found", 404);
  }

  try {
    const fileId = parseInt(ctx.params.id, 10);

    if (isNaN(fileId)) {
      throw new RequestError("Invalid file ID", 400);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Import fs module
    const fs = await import("fs");

    // Fetch file metadata from database
    const fileMetadata = await db("chat_attachments")
      .where({ id: fileId })
      .first();

    if (!fileMetadata) {
      throw new RequestError("File not found", 404);
    }

    // Check if user has access to the location
    if (fileMetadata.location_id !== ctx.state.locationId) {
      throw new RequestError("Access denied", 403);
    }

    // Serve the file
    ctx.set("Content-Type", fileMetadata.content_type);
    ctx.set(
      "Content-Disposition",
      `inline; filename="${fileMetadata.original_name}"`
    );
    ctx.body = fs.createReadStream(fileMetadata.path);
  } catch (error) {
    logger.error({
      message: "File serving error",
      error: error instanceof Error ? error.message : "Unknown error",
      fileId: ctx.params.id,
      locationId: ctx.state.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Failed to serve file",
    };
  }
});

/**
 * @swagger
 * /public/products/users:
 *   post:
 *     summary: Create VIP User
 *     description: Create a new user with VIP status for the location
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - external_id
 *             properties:
 *               external_id:
 *                 type: string
 *                 description: Unique identifier for the user
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               phone:
 *                 type: string
 *                 description: User's phone number
 *               first_name:
 *                 type: string
 *                 description: User's first name
 *               last_name:
 *                 type: string
 *                 description: User's last name
 *               timezone:
 *                 type: string
 *                 description: User's timezone
 *               locale:
 *                 type: string
 *                 description: User's locale
 *               vip:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the user is a VIP customer
 *               data:
 *                 type: object
 *                 additionalProperties: true
 *                 description: Additional user data
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 external_id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 phone:
 *                   type: string
 *                 vip:
 *                   type: boolean
 *                 location_id:
 *                   type: integer
 *                 created_at:
 *                   type: string
 *                   format: date-time
 *                 data:
 *                   type: object
 *       400:
 *         description: Invalid request or user already exists
 *       401:
 *         description: API key missing or invalid
 *       500:
 *         description: Internal server error
 */
router.post("/users", async (ctx) => {
  if (!ctx.state.locationId || !ctx.state.location) {
    throw new RequestError("Location not found", 404);
  }

  try {
    // Validate request body
    const payload = validate<VipUserParams>(vipUserSchema, ctx.request.body);

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Check if user already exists with this external_id for this location
    const existingUser = await db("users")
      .where("external_id", payload.external_id)
      .where("location_id", ctx.state.locationId)
      .first();

    if (existingUser) {
      logger.warn({
        message: "User already exists",
        external_id: payload.external_id,
        location_id: ctx.state.locationId,
      });
      throw new RequestError("User with this external_id already exists", 400);
    }

    // Prepare user data
    const userData = {
      external_id: payload.external_id,
      email: payload.email || null,
      phone: payload.phone || null,
      timezone: payload.timezone || null,
      locale: payload.locale || null,
      location_id: ctx.state.locationId,
      vip: payload.vip !== undefined ? payload.vip : true, // Default to VIP if not specified
      data: {
        first_name: payload.first_name || null,
        last_name: payload.last_name || null,
        ...payload.data,
      },
      created_at: new Date(),
      updated_at: new Date(),
    };

    // Insert the user
    const [userId] = await db("users").insert(userData);

    // Retrieve the created user
    const createdUser = await db("users").where("id", userId).first();

    logger.info({
      message: "VIP user created successfully",
      user_id: userId,
      external_id: payload.external_id,
      location_id: ctx.state.locationId,
      vip: createdUser.vip,
    });

    ctx.status = 201;
    ctx.body = {
      id: createdUser.id,
      external_id: createdUser.external_id,
      email: createdUser.email,
      phone: createdUser.phone,
      vip: createdUser.vip,
      location_id: createdUser.location_id,
      created_at: createdUser.created_at,
      data: createdUser.data,
    };
  } catch (error) {
    logger.error({
      message: "Error creating VIP user",
      error: error instanceof Error ? error.message : "Unknown error",
      location_id: ctx.state.locationId,
      payload: ctx.request.body,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Failed to create user",
    };
  }
});
/**
 * @swagger
 * /public/products/menu-settings:
 *   get:
 *     summary: Get Menu Settings
 *     description: Get active menu settings for the location, optionally filtered by type
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [carousel_slide, promo_content, announcement]
 *         description: Optional filter by menu setting type
 *     responses:
 *       200:
 *         description: List of menu settings
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   type:
 *                     type: string
 *                   title:
 *                     type: string
 *                   description:
 *                     type: string
 *                   image_url:
 *                     type: string
 *                   link:
 *                     type: string
 *                   order:
 *                     type: integer
 *       401:
 *         description: API key missing or invalid
 *       500:
 *         description: Internal server error
 */
router.get("/menu-settings", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    const typeFilter = ctx.query.type as string | undefined;
    const now = new Date();

    // Build query similar to admin endpoint but with public filters
    let query = db("menu_settings")
      .where("location_id", ctx.state.locationId)
      .where("active", true)
      .where(function () {
        this.whereNull("start_date").orWhere("start_date", "<=", now);
      })
      .where(function () {
        this.whereNull("end_date").orWhere("end_date", ">=", now);
      })
      .orderBy("type", "asc")
      .orderBy("order", "asc")
      .orderBy("created_at", "desc");

    // Apply type filter if provided
    if (typeFilter) {
      query = query.where("type", typeFilter);
    }

    const settings = await query;

    logger.info({
      message: "Public menu settings retrieved",
      location_id: ctx.state.locationId,
      settings_count: settings.length,
      type_filter: typeFilter,
    });

    ctx.body = settings;
  } catch (error) {
    logger.error({
      message: "Error retrieving public menu settings",
      error: error instanceof Error ? error.message : "Unknown error",
      location_id: ctx.state?.locationId,
      type_filter: ctx.query.type,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
});

/**
 * @swagger
 * /public/products/featured:
 *   get:
 *     summary: Get Featured Products (Public)
 *     description: Retrieves featured products for public display
 *     tags: [PublicProduct, Featured]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: top_picks_only
 *         schema:
 *           type: boolean
 *         description: Filter to only show top picks
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of featured products to return
 *     responses:
 *       200:
 *         description: Featured products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 featured_products:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 top_picks:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 total:
 *                   type: integer
 */
router.get("/featured", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const topPicksOnly = ctx.query.top_picks_only === "true";
    const limit = Math.min(
      50,
      Math.max(1, parseInt(ctx.query.limit as string) || 10)
    );

    logger.info({
      message: "Getting featured products (public)",
      locationId: ctx.state.locationId,
      topPicksOnly,
      limit,
    });

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let query = db("featured_products")
      .where("featured_products.location_id", ctx.state.locationId)
      .where("featured_products.active", true)
      .leftJoin("products", "featured_products.product_id", "products.id")
      .where("products.is_active", true)
      .select(
        "featured_products.*",
        "products.*",
        db.raw("featured_products.id as featured_id")
      )
      .orderBy("featured_products.sort_order", "asc")
      .limit(limit);

    if (topPicksOnly) {
      query = query.where("featured_products.is_top_pick", true);
    }

    const featuredProducts = await query;

    // Transform and process products similar to regular product endpoints
    const processedProducts = processProductData(
      featuredProducts,
      ctx.state.locationId
    );

    // Separate into featured and top picks for convenience
    const allFeatured = processedProducts.map((product: any) => ({
      ...product,
      is_featured: true,
      is_top_pick: product.is_top_pick || false,
      sort_order: product.sort_order || 0,
    }));

    const topPicks = allFeatured.filter((product: any) => product.is_top_pick);

    logger.info({
      message: "Retrieved featured products (public)",
      total: allFeatured.length,
      topPicks: topPicks.length,
      locationId: ctx.state.locationId,
    });

    ctx.body = {
      featured_products: topPicksOnly ? topPicks : allFeatured,
      top_picks: topPicks,
      total: allFeatured.length,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching featured products (public)",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state?.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = { error: "Failed to fetch featured products" };
  }
});

/**
 * @swagger
 * /public/products/top-picks:
 *   get:
 *     summary: Get Top Pick Products (Public)
 *     description: Retrieves products marked as top picks for public display
 *     tags: [PublicProduct, Featured]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Maximum number of top picks to return
 *     responses:
 *       200:
 *         description: Top picks retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 top_picks:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 total:
 *                   type: integer
 */
router.get("/top-picks", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const limit = Math.min(
      20,
      Math.max(1, parseInt(ctx.query.limit as string) || 5)
    );

    logger.info({
      message: "Getting top pick products (public)",
      locationId: ctx.state.locationId,
      limit,
    });

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    const topPickProducts = await db("featured_products")
      .where("featured_products.location_id", ctx.state.locationId)
      .where("featured_products.active", true)
      .where("featured_products.is_top_pick", true)
      .leftJoin("products", "featured_products.product_id", "products.id")
      .where("products.is_active", true)
      .select(
        "featured_products.*",
        "products.*",
        db.raw("featured_products.id as featured_id")
      )
      .orderBy("featured_products.sort_order", "asc")
      .limit(limit);

    // Transform and process products
    const processedProducts = processProductData(
      topPickProducts,
      ctx.state.locationId
    );

    const topPicks = processedProducts.map((product: any) => ({
      ...product,
      is_featured: true,
      is_top_pick: true,
      sort_order: product.sort_order || 0,
    }));

    logger.info({
      message: "Retrieved top pick products (public)",
      count: topPicks.length,
      locationId: ctx.state.locationId,
    });

    ctx.body = {
      top_picks: topPicks,
      total: topPicks.length,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching top pick products (public)",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state?.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = { error: "Failed to fetch top pick products" };
  }
});

/**
 * @swagger
 * /public/products/{id}:
 *   get:
 *     summary: Get Single Product
 *     description: Get details of a specific product by ID
 *     tags: [PublicProduct]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID or meta_sku
 *     responses:
 *       200:
 *         description: Product details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       401:
 *         description: API key missing or invalid
 *       404:
 *         description: Product not found
 *       500:
 *         description: Internal server error
 */
router.get("/:id", async (ctx) => {
  try {
    if (!ctx.state.locationId) {
      throw new RequestError("Location not found", 404);
    }

    const productId = ctx.params.id;
    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Check if we want grouped format (default true, same as main endpoint)
    const grouped = ctx.query.grouped !== "false";

    if (grouped) {
      // GROUPED PRODUCT FORMAT (DEFAULT)
      // Try to find products by meta_sku first, then by ID - only active products
      let products = await db("products")
        .where("products.location_id", ctx.state.locationId)
        .where("products.is_active", true)
        .where("products.meta_sku", productId)
        .leftJoin("featured_products", function () {
          this.on("featured_products.product_id", "=", "products.id").andOn(
            "featured_products.location_id",
            "=",
            "products.location_id"
          );
        })
        .select(
          "products.*",
          db.raw(
            "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
          ),
          db.raw(
            "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
          ),
          db.raw("featured_products.sort_order as featured_sort_order"),
          db.raw("COALESCE(featured_products.active, false) as featured_active")
        );

      if (products.length === 0) {
        // Try finding by ID and get all products with the same meta_sku
        const singleProduct = await db("products")
          .where("products.location_id", ctx.state.locationId)
          .where("products.is_active", true)
          .where("products.id", productId)
          .leftJoin("featured_products", function () {
            this.on("featured_products.product_id", "=", "products.id").andOn(
              "featured_products.location_id",
              "=",
              "products.location_id"
            );
          })
          .select(
            "products.*",
            db.raw(
              "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
            ),
            db.raw(
              "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
            ),
            db.raw("featured_products.sort_order as featured_sort_order"),
            db.raw(
              "COALESCE(featured_products.active, false) as featured_active"
            )
          )
          .first();

        if (singleProduct) {
          products = await db("products")
            .where("products.location_id", ctx.state.locationId)
            .where("products.is_active", true)
            .where("products.meta_sku", singleProduct.meta_sku)
            .leftJoin("featured_products", function () {
              this.on("featured_products.product_id", "=", "products.id").andOn(
                "featured_products.location_id",
                "=",
                "products.location_id"
              );
            })
            .select(
              "products.*",
              db.raw(
                "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
              ),
              db.raw(
                "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
              ),
              db.raw("featured_products.sort_order as featured_sort_order"),
              db.raw(
                "COALESCE(featured_products.active, false) as featured_active"
              )
            );
        }
      }

      if (products.length === 0) {
        throw new RequestError("Product not found", 404);
      }

      // Group products by meta_sku (should be the same for all)
      const metaSku = products[0].meta_sku;
      const baseProduct = products[0];

      // Calculate price range
      const prices = products
        .map((v) => v.latest_price || 0)
        .filter((p) => p > 0);
      const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
      const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

      // Create variant objects
      const productVariants: ProductVariant[] = products.map((variant) => ({
        id: variant.id,
        product_id: variant.product_id,
        display_weight: variant.display_weight || "",
        latest_price: variant.latest_price || 0,
        medical: variant.medical || false,
        recreational: variant.recreational || false,
        external_id: variant.external_id,
        url: variant.url,
        inventory_quantity: variant.inventory_quantity,
        out_of_stock: variant.out_of_stock,
        slug: variant.slug,
      }));

      const groupedProduct: GroupedProduct = {
        meta_sku: metaSku,
        product_name: baseProduct.product_name,
        brand_name: baseProduct.brand_name,
        category: baseProduct.category,
        subcategory: baseProduct.subcategory,
        description: baseProduct.product_description,
        image_url: baseProduct.image_url,
        product_tags: baseProduct.product_tags,
        percentage_thc: baseProduct.percentage_thc,
        percentage_cbd: baseProduct.percentage_cbd,
        rating: baseProduct.rating,
        reviews_count: baseProduct.reviews_count,
        variants: productVariants,
        base_price: minPrice,
        price_range: {
          min: minPrice,
          max: maxPrice,
        },
        mood: baseProduct.mood,
        effects: baseProduct.effects,
        product_description: baseProduct.product_description,
        short_description: baseProduct.short_description,
        inventory_quantity: baseProduct.inventory_quantity,
        out_of_stock: baseProduct.out_of_stock,
        slug: baseProduct.slug,
        // Featured product fields
        is_featured: baseProduct.is_featured || false,
        is_top_pick: baseProduct.is_top_pick || false,
        featured_sort_order: baseProduct.featured_sort_order,
        featured_active: baseProduct.featured_active || false,
      };

      logger.info({
        message: "PublicProductController: Single grouped product retrieved",
        product_id: productId,
        meta_sku: metaSku,
        variants_count: productVariants.length,
        location_id: ctx.state.locationId,
      });

      ctx.body = groupedProduct;
    } else {
      // INDIVIDUAL PRODUCT FORMAT (LEGACY)
      // Try to find product by ID first, then by meta_sku - only active products
      let product = await db("products")
        .where("products.location_id", ctx.state.locationId)
        .where("products.is_active", true)
        .where("products.id", productId)
        .leftJoin("featured_products", function () {
          this.on("featured_products.product_id", "=", "products.id").andOn(
            "featured_products.location_id",
            "=",
            "products.location_id"
          );
        })
        .select(
          "products.*",
          db.raw(
            "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
          ),
          db.raw(
            "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
          ),
          db.raw("featured_products.sort_order as featured_sort_order"),
          db.raw("COALESCE(featured_products.active, false) as featured_active")
        )
        .first();

      if (!product) {
        // Try finding by meta_sku if not found by ID
        product = await db("products")
          .where("products.location_id", ctx.state.locationId)
          .where("products.is_active", true)
          .where("products.meta_sku", productId)
          .leftJoin("featured_products", function () {
            this.on("featured_products.product_id", "=", "products.id").andOn(
              "featured_products.location_id",
              "=",
              "products.location_id"
            );
          })
          .select(
            "products.*",
            db.raw(
              "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
            ),
            db.raw(
              "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
            ),
            db.raw("featured_products.sort_order as featured_sort_order"),
            db.raw(
              "COALESCE(featured_products.active, false) as featured_active"
            )
          )
          .first();
      }

      if (!product) {
        throw new RequestError("Product not found", 404);
      }

      logger.info({
        message: "PublicProductController: Single individual product retrieved",
        product_id: productId,
        location_id: ctx.state.locationId,
        found_by: product.id.toString() === productId ? "id" : "meta_sku",
      });

      ctx.body = product;
    }
  } catch (error) {
    logger.error({
      message: "Error in PublicProductController GET /:id",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      product_id: ctx.params.id,
      location_id: ctx.state?.locationId,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = { error: "Internal server error" };
    }
  }
});

export default router;
