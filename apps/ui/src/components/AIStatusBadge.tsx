import React, { useState } from "react";
import { Product } from "../types";
import {
  validateProductForAI,
  AIReadinessResult,
} from "../utils/productValidation";
import { SparklesIcon } from "../ui/icons";
import Button from "../ui/Button";

interface TooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  position?: "top" | "bottom" | "left" | "right";
}

const Tooltip: React.FC<TooltipProps> = ({
  children,
  content,
  position = "top",
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const positionClasses = {
    top: "bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2",
    bottom: "top-full left-1/2 transform -translate-x-1/2 translate-y-2",
    left: "right-full top-1/2 transform -translate-y-1/2 -translate-x-2",
    right: "left-full top-1/2 transform -translate-y-1/2 translate-x-2",
  };

  const arrowClasses = {
    top: "top-full left-1/2 transform -translate-x-1/2 border-t-gray-800 border-t-4 border-l-4 border-r-4 border-l-transparent border-r-transparent",
    bottom:
      "bottom-full left-1/2 transform -translate-x-1/2 border-b-gray-800 border-b-4 border-l-4 border-r-4 border-l-transparent border-r-transparent",
    left: "left-full top-1/2 transform -translate-y-1/2 border-l-gray-800 border-l-4 border-t-4 border-b-4 border-t-transparent border-b-transparent",
    right:
      "right-full top-1/2 transform -translate-y-1/2 border-r-gray-800 border-r-4 border-t-4 border-b-4 border-t-transparent border-b-transparent",
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      >
        {children}
      </div>
      {isVisible && (
        <div
          className={`absolute z-50 px-3 py-2 text-sm text-white bg-gray-800 rounded shadow-lg pointer-events-none ${positionClasses[position]}`}
          style={{ minWidth: "200px", maxWidth: "300px" }}
        >
          {content}
          <div className={`absolute ${arrowClasses[position]}`} />
        </div>
      )}
    </div>
  );
};

interface AIStatusBadgeProps {
  product: Partial<Product>;
  onEnhanceClick?: () => void;
  onEditClick?: () => void;
  showActions?: boolean;
  size?: "small" | "medium" | "large";
}

const AIStatusBadge: React.FC<AIStatusBadgeProps> = ({
  product,
  onEnhanceClick,
  onEditClick,
  showActions = false,
  size = "medium",
}) => {
  const aiStatus = validateProductForAI(product);

  const sizeClasses = {
    small: "px-2 py-1 text-xs",
    medium: "px-3 py-1 text-sm",
    large: "px-4 py-2 text-base",
  };

  const iconSizes = {
    small: "text-xs",
    medium: "text-sm",
    large: "text-base",
  };

  const tooltipContent = (
    <div>
      <div className="font-semibold mb-1">{aiStatus.label}</div>
      <div className="mb-2">{aiStatus.description}</div>

      {aiStatus.suggestions && aiStatus.suggestions.length > 0 && (
        <div className="mb-2">
          <div className="font-medium mb-1">Suggestions:</div>
          <ul className="list-disc list-inside text-xs">
            {aiStatus.suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}

      {showActions &&
        (aiStatus.status === "needs-improvement" ||
          aiStatus.status === "ready") && (
          <div className="border-t border-gray-600 pt-2 mt-2">
            <div className="flex gap-2">
              {onEditClick && (
                <Button
                  size="small"
                  variant="secondary"
                  onClick={onEditClick}
                  style={{ fontSize: "10px", padding: "2px 6px" }}
                >
                  Edit
                </Button>
              )}
              {onEnhanceClick && (
                <Button
                  size="small"
                  onClick={onEnhanceClick}
                  icon={<SparklesIcon />}
                  style={{ fontSize: "10px", padding: "2px 6px" }}
                >
                  Enhance
                </Button>
              )}
            </div>
          </div>
        )}
    </div>
  );

  return (
    <Tooltip content={tooltipContent}>
      <div
        className={`inline-flex items-center gap-1 rounded-full font-medium cursor-help ${sizeClasses[size]}`}
        style={{
          backgroundColor: aiStatus.bgColor,
          color: aiStatus.color,
          border: `1px solid ${aiStatus.color}20`,
        }}
      >
        <span className={iconSizes[size]}>{aiStatus.icon}</span>
        <span>{aiStatus.label}</span>
      </div>
    </Tooltip>
  );
};

export default AIStatusBadge;
